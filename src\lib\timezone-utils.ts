/**
 * Timezone utility functions for converting UTC dates to user timezone
 * and handling timezone-aware date filtering in SQL queries
 */

/**
 * Get timezone from request headers (Cloudflare provides this)
 */
export function getTimezoneFromRequest(request: Request): string {
  // Try to get timezone from Cloudflare headers
  const cfTimezone = request.headers.get('cf-timezone');
  console.log('Debug: cf-timezone header:', cfTimezone);
  if (cfTimezone) {
    return cfTimezone;
  }

  // Try to get from custom header (if frontend sends it)
  const customTimezone = request.headers.get('x-user-timezone');
  console.log('Debug: x-user-timezone header:', customTimezone);
  if (customTimezone) {
    return customTimezone;
  }

  // Try to get from URL parameter
  const url = new URL(request.url);
  const urlTimezone = url.searchParams.get('timezone');
  console.log('Debug: timezone URL parameter:', urlTimezone);
  if (urlTimezone) {
    return urlTimezone;
  }

  // Default to UTC if no timezone information is available
  console.log('Debug: No timezone found, defaulting to UTC');
  return 'UTC';
}

/**
 * Convert UTC date string to user timezone
 */
export function convertUTCToUserTimezone(utcDateString: string, timezone: string): string {
  try {
    const utcDate = new Date(utcDateString);
    
    // Convert to user timezone
    const userDate = new Date(utcDate.toLocaleString('en-US', { timeZone: timezone }));
    
    return userDate.toISOString();
  } catch (error) {
    console.error('Error converting timezone:', error);
    return utcDateString; // Return original if conversion fails
  }
}

/**
 * Get timezone-aware date filter for SQL queries
 * This converts the date range to UTC based on user's timezone
 */
export function getTimezoneAwareDateFilter(
  dateRange: string,
  timezone: string,
  fieldName: string = 'scan_time'
): string {
  try {
    console.log(`Debug: Creating timezone-aware filter for range: ${dateRange}, timezone: ${timezone}`);

    // For simplicity and reliability, let's use a more straightforward approach
    // Calculate the start date in the user's timezone, then convert to UTC
    const now = new Date();
    let daysBack = 0;

    switch (dateRange) {
      case '1d':
        daysBack = 1;
        break;
      case '7d':
        daysBack = 7;
        break;
      case '30d':
        daysBack = 30;
        break;
      case '90d':
        daysBack = 90;
        break;
      case '1y':
        daysBack = 365;
        break;
      default:
        console.log('Debug: No date filter applied for range:', dateRange);
        return ''; // No filter for 'all' or unknown ranges
    }

    // Create start date in user's timezone (start of day)
    const userNow = new Date(now.toLocaleString('en-US', { timeZone: timezone }));
    const startOfDayInUserTz = new Date(userNow);
    startOfDayInUserTz.setHours(0, 0, 0, 0);
    startOfDayInUserTz.setDate(startOfDayInUserTz.getDate() - daysBack);

    // Convert to UTC by adjusting for timezone offset
    const userOffset = userNow.getTimezoneOffset();
    const utcOffset = now.getTimezoneOffset();
    const offsetDiff = userOffset - utcOffset;

    const utcStartDate = new Date(startOfDayInUserTz.getTime() + (offsetDiff * 60 * 1000));

    const filterClause = `AND ${fieldName} >= '${utcStartDate.toISOString()}'`;
    console.log(`Debug: Generated filter clause: ${filterClause}`);

    return filterClause;
  } catch (error) {
    console.error('Error creating timezone-aware date filter:', error);
    // Fallback to simple UTC-based filter
    const fallbackFilter = (() => {
      switch (dateRange) {
        case '1d':
          return `AND ${fieldName} >= datetime('now', '-1 day')`;
        case '7d':
          return `AND ${fieldName} >= datetime('now', '-7 days')`;
        case '30d':
          return `AND ${fieldName} >= datetime('now', '-30 days')`;
        case '90d':
          return `AND ${fieldName} >= datetime('now', '-90 days')`;
        case '1y':
          return `AND ${fieldName} >= datetime('now', '-1 year')`;
        default:
          return '';
      }
    })();

    console.log(`Debug: Using fallback filter: ${fallbackFilter}`);
    return fallbackFilter;
  }
}

/**
 * Get timezone offset in milliseconds
 */
function getTimezoneOffset(timezone: string, date: Date): number {
  try {
    const utcDate = new Date(date.toISOString());
    const tzDate = new Date(date.toLocaleString('en-US', { timeZone: timezone }));
    return utcDate.getTime() - tzDate.getTime();
  } catch (error) {
    return 0; // Return 0 offset if calculation fails
  }
}

/**
 * Convert array of date-based data to user timezone
 */
export function convertDataToUserTimezone<T extends { date?: string; scan_time?: string; created_at?: string }>(
  data: T[], 
  timezone: string
): T[] {
  return data.map(item => {
    const converted = { ...item };
    
    // Convert date field if present
    if (item.date) {
      converted.date = convertUTCToUserTimezone(item.date, timezone).split('T')[0];
    }
    
    // Convert scan_time field if present
    if (item.scan_time) {
      converted.scan_time = convertUTCToUserTimezone(item.scan_time, timezone);
    }
    
    // Convert created_at field if present
    if (item.created_at) {
      converted.created_at = convertUTCToUserTimezone(item.created_at, timezone);
    }
    
    return converted;
  });
}

/**
 * Get timezone-aware hour extraction for SQL queries
 * This adjusts the hour extraction to account for timezone differences
 */
export function getTimezoneAwareHourExtraction(timezone: string, fieldName: string = 'scan_time'): string {
  try {
    // Calculate timezone offset in hours
    const now = new Date();
    const utcHour = now.getUTCHours();
    const localHour = parseInt(now.toLocaleString('en-US', { 
      timeZone: timezone, 
      hour: '2-digit', 
      hour12: false 
    }));
    
    const offsetHours = localHour - utcHour;
    
    if (offsetHours === 0) {
      return `CAST(strftime('%H', ${fieldName}) AS INTEGER)`;
    } else if (offsetHours > 0) {
      return `CAST((strftime('%H', ${fieldName}) + ${offsetHours}) % 24 AS INTEGER)`;
    } else {
      return `CAST((strftime('%H', ${fieldName}) + 24 + ${offsetHours}) % 24 AS INTEGER)`;
    }
  } catch (error) {
    console.error('Error creating timezone-aware hour extraction:', error);
    // Fallback to UTC hour extraction
    return `CAST(strftime('%H', ${fieldName}) AS INTEGER)`;
  }
}

/**
 * Get timezone-aware date extraction for SQL queries
 */
export function getTimezoneAwareDateExtraction(timezone: string, fieldName: string = 'scan_time'): string {
  try {
    // For simplicity, we'll use a basic offset calculation
    // In production, you might want to use a more sophisticated approach
    const now = new Date();
    const offsetMs = getTimezoneOffset(timezone, now);
    const offsetHours = Math.round(offsetMs / (1000 * 60 * 60));
    
    if (offsetHours === 0) {
      return `DATE(${fieldName})`;
    } else if (offsetHours > 0) {
      return `DATE(${fieldName}, '+${offsetHours} hours')`;
    } else {
      return `DATE(${fieldName}, '${offsetHours} hours')`;
    }
  } catch (error) {
    console.error('Error creating timezone-aware date extraction:', error);
    return `DATE(${fieldName})`;
  }
}

/**
 * Format date for display in user's timezone
 */
export function formatDateForDisplay(
  utcDateString: string, 
  timezone: string, 
  options: Intl.DateTimeFormatOptions = {}
): string {
  try {
    const date = new Date(utcDateString);
    return date.toLocaleString('en-US', {
      timeZone: timezone,
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      ...options
    });
  } catch (error) {
    console.error('Error formatting date for display:', error);
    return utcDateString;
  }
}

/**
 * Validate timezone string
 */
export function isValidTimezone(timezone: string): boolean {
  try {
    Intl.DateTimeFormat(undefined, { timeZone: timezone });
    return true;
  } catch (error) {
    return false;
  }
}
