import type { 
  DatabaseQRCode, 
  DatabaseScanAnalytics, 
  QRCode, 
  DashboardMetrics, 
  QRAnalytics,
  ScanByDate,
  ScanByLocation,
  ScanByDevice,
  ScanByReferrer,
  RecentScan,
  PaginationInfo
} from '../types/dashboard';

// Database connection helper
export function getDatabase(env: any): D1Database {
  if (!env.DB) {
    throw new Error('Database binding not found. Make sure D1 database is properly configured.');
  }
  return env.DB;
}

// Helper function to get timezone offset for SQLite datetime operations
function getTimezoneOffset(timezone: string): string {
  try {
    // For IANA timezone names (e.g., "America/New_York"), calculate offset
    if (timezone.includes('/')) {
      const now = new Date();
      const utc = new Date(now.getTime() + (now.getTimezoneOffset() * 60000));
      const targetTime = new Date(utc.toLocaleString('en-US', { timeZone: timezone }));
      const offsetMs = targetTime.getTime() - utc.getTime();
      const offsetHours = offsetMs / (1000 * 60 * 60);

      if (offsetHours >= 0) {
        return `+${Math.abs(offsetHours)} hours`;
      } else {
        return `-${Math.abs(offsetHours)} hours`;
      }
    }

    // Fallback to common timezone abbreviations
    const timezoneOffsets: Record<string, string> = {
      'UTC': '+0 hours',
      'EST': '-5 hours',
      'CST': '-6 hours',
      'MST': '-7 hours',
      'PST': '-8 hours',
      'EDT': '-4 hours',
      'CDT': '-5 hours',
      'MDT': '-6 hours',
      'PDT': '-7 hours',
      'GMT': '+0 hours',
      'CET': '+1 hours',
      'JST': '+9 hours',
      'AEST': '+10 hours',
      'IST': '+5.5 hours'
    };

    return timezoneOffsets[timezone] || '+0 hours';
  } catch (error) {
    console.error('Error calculating timezone offset:', error);
    return '+0 hours'; // Default to UTC
  }
}

// Transform database QR code to dashboard QR code format
function transformQRCode(dbQRCode: DatabaseQRCode, scanCount: number = 0, lastScanned?: string): QRCode {
  return {
    ...dbQRCode,
    scanCount,
    status: dbQRCode.dynamic === 1 ? 'active' : 'inactive',
    lastScanned
  };
}

// Get dashboard metrics with timezone support (optimized)
export async function getDashboardMetrics(db: D1Database, userId?: string, userTimezone?: string): Promise<DashboardMetrics> {
  try {
    // Default to UTC if no timezone provided
    const timezone = userTimezone || 'UTC';
    const timezoneOffset = getTimezoneOffset(timezone);

    // Optimized: Combine all metrics into a single query
    const metricsQuery = userId
      ? `SELECT
         (SELECT COUNT(*) FROM qr_codes WHERE user_id = ? AND dynamic = 1) as total_active_qr_codes,
         (SELECT COUNT(*) FROM qr_code_scan_analytics sa JOIN qr_codes qr ON sa.qr_code_id = qr.id WHERE qr.user_id = ?) as total_scan_count,
         (SELECT COUNT(*) FROM qr_code_scan_analytics sa JOIN qr_codes qr ON sa.qr_code_id = qr.id WHERE qr.user_id = ? AND DATE(datetime(sa.scan_time, '${timezoneOffset}')) = DATE(datetime('now', '${timezoneOffset}'))) as today_scan_count,
         (SELECT COUNT(*) FROM qr_code_scan_analytics sa JOIN qr_codes qr ON sa.qr_code_id = qr.id WHERE qr.user_id = ? AND DATE(datetime(sa.scan_time, '${timezoneOffset}')) >= DATE(datetime('now', '${timezoneOffset}', '-7 days'))) as current_week_scans,
         (SELECT COUNT(*) FROM qr_code_scan_analytics sa JOIN qr_codes qr ON sa.qr_code_id = qr.id WHERE qr.user_id = ? AND DATE(datetime(sa.scan_time, '${timezoneOffset}')) >= DATE(datetime('now', '${timezoneOffset}', '-14 days')) AND DATE(datetime(sa.scan_time, '${timezoneOffset}')) < DATE(datetime('now', '${timezoneOffset}', '-7 days'))) as previous_week_scans,
         (SELECT COUNT(*) FROM qr_code_scan_analytics sa JOIN qr_codes qr ON sa.qr_code_id = qr.id WHERE qr.user_id = ? AND DATE(datetime(sa.scan_time, '${timezoneOffset}')) >= DATE(datetime('now', '${timezoneOffset}', '-30 days'))) as current_month_scans,
         (SELECT COUNT(*) FROM qr_code_scan_analytics sa JOIN qr_codes qr ON sa.qr_code_id = qr.id WHERE qr.user_id = ? AND DATE(datetime(sa.scan_time, '${timezoneOffset}')) >= DATE(datetime('now', '${timezoneOffset}', '-60 days')) AND DATE(datetime(sa.scan_time, '${timezoneOffset}')) < DATE(datetime('now', '${timezoneOffset}', '-30 days'))) as previous_month_scans`
      : `SELECT
         (SELECT COUNT(*) FROM qr_codes WHERE dynamic = 1) as total_active_qr_codes,
         (SELECT COUNT(*) FROM qr_code_scan_analytics) as total_scan_count,
         (SELECT COUNT(*) FROM qr_code_scan_analytics WHERE DATE(datetime(scan_time, '${timezoneOffset}')) = DATE(datetime('now', '${timezoneOffset}'))) as today_scan_count,
         (SELECT COUNT(*) FROM qr_code_scan_analytics WHERE DATE(datetime(scan_time, '${timezoneOffset}')) >= DATE(datetime('now', '${timezoneOffset}', '-7 days'))) as current_week_scans,
         (SELECT COUNT(*) FROM qr_code_scan_analytics WHERE DATE(datetime(scan_time, '${timezoneOffset}')) >= DATE(datetime('now', '${timezoneOffset}', '-14 days')) AND DATE(datetime(scan_time, '${timezoneOffset}')) < DATE(datetime('now', '${timezoneOffset}', '-7 days'))) as previous_week_scans,
         (SELECT COUNT(*) FROM qr_code_scan_analytics WHERE DATE(datetime(scan_time, '${timezoneOffset}')) >= DATE(datetime('now', '${timezoneOffset}', '-30 days'))) as current_month_scans,
         (SELECT COUNT(*) FROM qr_code_scan_analytics WHERE DATE(datetime(scan_time, '${timezoneOffset}')) >= DATE(datetime('now', '${timezoneOffset}', '-60 days')) AND DATE(datetime(scan_time, '${timezoneOffset}')) < DATE(datetime('now', '${timezoneOffset}', '-30 days'))) as previous_month_scans`;

    const metricsParams = userId ? [userId, userId, userId, userId, userId, userId, userId] : [];
    const metricsResult = await db.prepare(metricsQuery).bind(...metricsParams).first();

    const totalActiveQRCodes = Number((metricsResult as any)?.total_active_qr_codes) || 0;
    const totalScanCount = Number((metricsResult as any)?.total_scan_count) || 0;
    const todayScanCount = Number((metricsResult as any)?.today_scan_count) || 0;
    const currentWeek = Number((metricsResult as any)?.current_week_scans) || 0;
    const previousWeek = Number((metricsResult as any)?.previous_week_scans) || 0;
    const currentMonth = Number((metricsResult as any)?.current_month_scans) || 0;
    const previousMonth = Number((metricsResult as any)?.previous_month_scans) || 0;

    // Calculate growth percentages
    const weeklyGrowth = previousWeek > 0 ? ((currentWeek - previousWeek) / previousWeek) * 100 : 0;
    const monthlyGrowth = previousMonth > 0 ? ((currentMonth - previousMonth) / previousMonth) * 100 : 0;

    return {
      totalActiveQRCodes: Number(totalActiveQRCodes) || 0,
      totalScanCount: Number(totalScanCount) || 0,
      todayScanCount: Number(todayScanCount) || 0,
      weeklyGrowth: Number(weeklyGrowth) || 0,
      monthlyGrowth: Number(monthlyGrowth) || 0
    };
  } catch (error) {
    console.error('Error fetching dashboard metrics:', error);
    throw new Error('Failed to fetch dashboard metrics');
  }
}

// Get paginated QR codes list
export async function getQRCodesList(
  db: D1Database,
  page: number = 1,
  limit: number = 10,
  userId?: string
): Promise<{ qrCodes: QRCode[], pagination: PaginationInfo }> {
  try {
    const offset = (page - 1) * limit;

    // Get total count
    const countQuery = userId
      ? 'SELECT COUNT(*) as total FROM qr_codes WHERE user_id = ?'
      : 'SELECT COUNT(*) as total FROM qr_codes';
    const countParams = userId ? [userId] : [];
    const countResult = await db.prepare(countQuery).bind(...countParams).first();
    const totalItems = Number((countResult as any)?.total) || 0;
    const totalPages = Math.ceil(totalItems / limit);

    // Get QR codes with scan counts
    const qrCodesQuery = userId
      ? `SELECT
         qr.*,
         COUNT(sa.id) as scan_count,
         MAX(sa.scan_time) as last_scanned
         FROM qr_codes qr
         LEFT JOIN qr_code_scan_analytics sa ON qr.id = sa.qr_code_id
         WHERE qr.user_id = ?
         GROUP BY qr.id
         ORDER BY qr.created_at DESC
         LIMIT ? OFFSET ?`
      : `SELECT
         qr.*,
         COUNT(sa.id) as scan_count,
         MAX(sa.scan_time) as last_scanned
         FROM qr_codes qr
         LEFT JOIN qr_code_scan_analytics sa ON qr.id = sa.qr_code_id
         GROUP BY qr.id
         ORDER BY qr.created_at DESC
         LIMIT ? OFFSET ?`;

    const queryParams = userId ? [userId, limit, offset] : [limit, offset];
    const qrCodesResult = await db.prepare(qrCodesQuery).bind(...queryParams).all();

    const qrCodes = qrCodesResult.results.map((row: any) => 
      transformQRCode(row as DatabaseQRCode, row.scan_count || 0, row.last_scanned)
    );

    return {
      qrCodes,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems,
        itemsPerPage: limit
      }
    };
  } catch (error) {
    console.error('Error fetching QR codes list:', error);
    throw new Error('Failed to fetch QR codes list');
  }
}

// Get QR code analytics
export async function getQRCodeAnalytics(db: D1Database, qrCodeId: string, userId?: string): Promise<{ qrCode: QRCode, analytics: QRAnalytics }> {
  try {
    // Get QR code details with optional user filtering
    const qrCodeQuery = userId
      ? `SELECT qr.*, COUNT(sa.id) as scan_count, MAX(sa.scan_time) as last_scanned
         FROM qr_codes qr
         LEFT JOIN qr_code_scan_analytics sa ON qr.id = sa.qr_code_id
         WHERE qr.id = ? AND qr.user_id = ?
         GROUP BY qr.id`
      : `SELECT qr.*, COUNT(sa.id) as scan_count, MAX(sa.scan_time) as last_scanned
         FROM qr_codes qr
         LEFT JOIN qr_code_scan_analytics sa ON qr.id = sa.qr_code_id
         WHERE qr.id = ?
         GROUP BY qr.id`;

    const queryParams = userId ? [qrCodeId, userId] : [qrCodeId];
    const qrCodeResult = await db.prepare(qrCodeQuery).bind(...queryParams).first();

    if (!qrCodeResult) {
      throw new Error('QR code not found');
    }

    const qrCode = transformQRCode(qrCodeResult as any, Number((qrCodeResult as any).scan_count) || 0, (qrCodeResult as any).last_scanned);

    // Get total and unique scans
    const scansStatsQuery = `
      SELECT
        COUNT(*) as total_scans,
        COUNT(DISTINCT ip) as unique_scans
      FROM qr_code_scan_analytics
      WHERE qr_code_id = ?
    `;
    const scansStatsResult = await db.prepare(scansStatsQuery).bind(qrCodeId).first();
    const totalScans = Number((scansStatsResult as any)?.total_scans) || 0;
    const uniqueScans = Number((scansStatsResult as any)?.unique_scans) || 0;

    // Get scans by date (last 30 days)
    const scansByDateQuery = `
      SELECT
        DATE(scan_time) as date,
        COUNT(*) as scans
      FROM qr_code_scan_analytics
      WHERE qr_code_id = ? AND DATE(scan_time) >= DATE('now', '-30 days')
      GROUP BY DATE(scan_time)
      ORDER BY date ASC
    `;
    const scansByDateResult = await db.prepare(scansByDateQuery).bind(qrCodeId).all();
    const scansByDate: ScanByDate[] = scansByDateResult.results.map((row: any) => ({
      date: row.date,
      scans: row.scans
    }));

    // Get scans by location
    const scansByLocationQuery = `
      SELECT
        country,
        city,
        COUNT(*) as scans,
        ROUND((COUNT(*) * 100.0 / ?), 1) as percentage
      FROM qr_code_scan_analytics
      WHERE qr_code_id = ? AND country IS NOT NULL
      GROUP BY country, city
      ORDER BY scans DESC
      LIMIT 10
    `;
    const scansByLocationResult = await db.prepare(scansByLocationQuery).bind(totalScans || 1, qrCodeId).all();
    const scansByLocation: ScanByLocation[] = scansByLocationResult.results.map((row: any) => ({
      country: row.country,
      city: row.city,
      scans: row.scans,
      percentage: row.percentage
    }));

    // Get scans by device
    const scansByDeviceQuery = `
      SELECT
        COALESCE(device, 'Unknown') as device,
        COUNT(*) as scans,
        ROUND((COUNT(*) * 100.0 / ?), 1) as percentage
      FROM qr_code_scan_analytics
      WHERE qr_code_id = ?
      GROUP BY device
      ORDER BY scans DESC
      LIMIT 10
    `;
    const scansByDeviceResult = await db.prepare(scansByDeviceQuery).bind(totalScans || 1, qrCodeId).all();
    const scansByDevice: ScanByDevice[] = scansByDeviceResult.results.map((row: any) => ({
      device: row.device,
      scans: row.scans,
      percentage: row.percentage
    }));

    // Get scans by referrer
    const scansByReferrerQuery = `
      SELECT
        CASE
          WHEN referrer IS NULL OR referrer = '' THEN 'Direct'
          WHEN referrer LIKE '%facebook%' OR referrer LIKE '%instagram%' OR referrer LIKE '%twitter%' OR referrer LIKE '%linkedin%' THEN 'Social Media'
          WHEN referrer LIKE '%gmail%' OR referrer LIKE '%outlook%' OR referrer LIKE '%mail%' THEN 'Email'
          ELSE 'Other'
        END as referrer,
        COUNT(*) as scans,
        ROUND((COUNT(*) * 100.0 / ?), 1) as percentage
      FROM qr_code_scan_analytics
      WHERE qr_code_id = ?
      GROUP BY referrer
      ORDER BY scans DESC
    `;
    const scansByReferrerResult = await db.prepare(scansByReferrerQuery).bind(totalScans || 1, qrCodeId).all();
    const scansByReferrer: ScanByReferrer[] = scansByReferrerResult.results.map((row: any) => ({
      referrer: row.referrer,
      scans: row.scans,
      percentage: row.percentage
    }));

    // Get recent scans
    const recentScansQuery = `
      SELECT *
      FROM qr_code_scan_analytics
      WHERE qr_code_id = ?
      ORDER BY scan_time DESC
      LIMIT 20
    `;
    const recentScansResult = await db.prepare(recentScansQuery).bind(qrCodeId).all();
    const recentScans: RecentScan[] = recentScansResult.results.map((row: any) => row as RecentScan);

    const analytics: QRAnalytics = {
      qrCodeId,
      totalScans,
      uniqueScans,
      scansByDate,
      scansByLocation,
      scansByDevice,
      scansByReferrer,
      recentScans
    };

    return { qrCode, analytics };
  } catch (error) {
    console.error('Error fetching QR code analytics:', error);
    throw new Error('Failed to fetch QR code analytics');
  }
}
