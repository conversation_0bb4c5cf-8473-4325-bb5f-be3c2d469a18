/**
 * Utility functions for making API calls with timezone information
 */

/**
 * Get user's timezone
 */
export function getUserTimezone(): string {
  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  } catch (error) {
    console.warn('Failed to get user timezone, falling back to UTC:', error);
    return 'UTC';
  }
}

/**
 * Create headers with timezone information
 */
export function createTimezoneHeaders(additionalHeaders: Record<string, string> = {}): Record<string, string> {
  const userTimezone = getUserTimezone();
  
  return {
    'Content-Type': 'application/json',
    'X-User-Timezone': userTimezone,
    ...additionalHeaders
  };
}

/**
 * Add timezone parameter to URL
 */
export function addTimezoneToUrl(baseUrl: string): string {
  const userTimezone = getUserTimezone();
  const url = new URL(baseUrl, window.location.origin);
  url.searchParams.set('timezone', userTimezone);
  return url.toString();
}

/**
 * Make a timezone-aware fetch request
 */
export async function fetchWithTimezone(
  url: string, 
  options: RequestInit = {}
): Promise<Response> {
  const userTimezone = getUserTimezone();
  
  // Add timezone to URL if it's a GET request or no body
  let finalUrl = url;
  if (!options.method || options.method.toUpperCase() === 'GET') {
    finalUrl = addTimezoneToUrl(url);
  }
  
  // Merge headers with timezone information
  const headers = createTimezoneHeaders(options.headers as Record<string, string> || {});
  
  return fetch(finalUrl, {
    credentials: 'include',
    ...options,
    headers
  });
}

/**
 * Format date for display in user's timezone
 */
export function formatDateInUserTimezone(
  dateString: string, 
  options: Intl.DateTimeFormatOptions = {}
): string {
  try {
    const date = new Date(`${dateString}${dateString.toString().includes('Z') ? '' : 'Z'}`);
    const userTimezone = getUserTimezone();
    // console.log("🚀 ~ formatDateInUserTimezone ~ userTimezone:", userTimezone)
    
    return date.toLocaleString('en-US', {
      timeZone: userTimezone,
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      ...options
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateString;
  }
}

/**
 * Format time ago in user's timezone
 */
export function formatTimeAgoInUserTimezone(dateString: string): string {
  try {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return `${diffInSeconds}s ago`;
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}m ago`;
    } else if (diffInSeconds < 86400) {
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    } else {
      return `${Math.floor(diffInSeconds / 86400)}d ago`;
    }
  } catch (error) {
    console.error('Error formatting time ago:', error);
    return dateString;
  }
}

/**
 * Get timezone display name
 */
export function getTimezoneDisplayName(): string {
  try {
    const userTimezone = getUserTimezone();
    const now = new Date();
    
    // Get timezone abbreviation
    const shortName = now.toLocaleString('en-US', {
      timeZone: userTimezone,
      timeZoneName: 'short'
    }).split(' ').pop() || '';
    
    // Get timezone offset
    const offset = now.toLocaleString('en-US', {
      timeZone: userTimezone,
      timeZoneName: 'longOffset'
    }).split(' ').pop() || '';
    
    return `${userTimezone} (${shortName} ${offset})`;
  } catch (error) {
    console.error('Error getting timezone display name:', error);
    return getUserTimezone();
  }
}

/**
 * Analytics API helper functions
 */
export const analyticsApi = {
  /**
   * Fetch overview data with timezone
   */
  async fetchOverview(dateRange: string = '7d', qrCodeId?: string): Promise<any> {
    const qrCodeParam = qrCodeId ? `&qr_code_id=${qrCodeId}` : '';
    const response = await fetchWithTimezone(`/api/analytics/overview?range=${dateRange}${qrCodeParam}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch overview data');
    }
    
    return response.json();
  },

  /**
   * Fetch time-based data with timezone
   */
  async fetchTimeBased(dateRange: string = '7d', qrCodeId?: string): Promise<any> {
    const qrCodeParam = qrCodeId ? `&qr_code_id=${qrCodeId}` : '';
    const response = await fetchWithTimezone(`/api/analytics/time-based?range=${dateRange}${qrCodeParam}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch time-based data');
    }
    
    return response.json();
  },

  /**
   * Fetch geographic data with timezone
   */
  async fetchGeographic(dateRange: string = '7d', qrCodeId?: string): Promise<any> {
    const qrCodeParam = qrCodeId ? `&qr_code_id=${qrCodeId}` : '';
    const response = await fetchWithTimezone(`/api/analytics/geographic?range=${dateRange}${qrCodeParam}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch geographic data');
    }
    
    return response.json();
  },

  /**
   * Fetch device data with timezone
   */
  async fetchDevices(dateRange: string = '7d', qrCodeId?: string): Promise<any> {
    const qrCodeParam = qrCodeId ? `&qr_code_id=${qrCodeId}` : '';
    const response = await fetchWithTimezone(`/api/analytics/devices?range=${dateRange}${qrCodeParam}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch device data');
    }
    
    return response.json();
  },

  /**
   * Fetch activity feed with timezone
   */
  async fetchActivityFeed(dateRange: string = '7d', qrCodeId?: string, limit: number = 50): Promise<any> {
    const qrCodeParam = qrCodeId ? `&qr_code_id=${qrCodeId}` : '';
    const response = await fetchWithTimezone(`/api/analytics/activity-feed?range=${dateRange}${qrCodeParam}&limit=${limit}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch activity feed');
    }
    
    return response.json();
  },

  /**
   * Export data with timezone
   */
  async exportData(format: 'csv' | 'excel', dateRange: string = '7d', qrCodeId?: string): Promise<Blob> {
    const qrCodeParam = qrCodeId ? `&qr_code_id=${qrCodeId}` : '';
    const response = await fetchWithTimezone(`/api/analytics/export?format=${format}&range=${dateRange}${qrCodeParam}`);
    
    if (!response.ok) {
      throw new Error('Failed to export data');
    }
    
    return response.blob();
  }
};
