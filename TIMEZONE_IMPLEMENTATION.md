# Timezone Implementation Guide

This document explains how timezone handling has been implemented across all analytics APIs and components in QRAnalytica.

## Overview

All analytics data is stored in UTC timezone in the database, but displayed to users in their local timezone. This ensures:
- Consistent data storage regardless of user location
- Accurate time-based analytics for users in different timezones
- Proper date filtering based on user's local time

## Implementation Details

### 1. Backend (API Layer)

#### Timezone Utilities (`src/lib/timezone-utils.ts`)

Core functions for timezone handling:

- `getTimezoneFromRequest(request)` - Extracts timezone from request headers
- `getTimezoneAwareDateFilter(dateRange, timezone, fieldName)` - Creates SQL date filters in user timezone
- `convertUTCToUserTimezone(utcDateString, timezone)` - Converts UTC dates to user timezone
- `convertDataToUserTimezone(data, timezone)` - Batch converts array data
- `getTimezoneAwareHourExtraction(timezone, fieldName)` - SQL hour extraction with timezone offset
- `getTimezoneAwareDateExtraction(timezone, fieldName)` - SQL date extraction with timezone offset

#### Updated APIs

All analytics APIs have been updated to support timezone-aware queries:

1. **`/api/analytics/overview`** - Overview metrics with timezone filtering
2. **`/api/analytics/time-based`** - Hourly/daily/weekly/monthly data in user timezone
3. **`/api/analytics/geographic`** - Geographic data with timezone-aware date filtering
4. **`/api/analytics/devices`** - Device analytics with timezone filtering
5. **`/api/analytics/activity-feed`** - Activity feed with timezone-converted timestamps
6. **`/api/analytics/export`** - Data export with timezone-aware filtering

#### Timezone Detection

The APIs detect user timezone from multiple sources (in order of priority):

1. `cf-timezone` header (Cloudflare provides this automatically)
2. `x-user-timezone` header (custom header sent by frontend)
3. `timezone` URL parameter
4. Fallback to UTC if none available

### 2. Frontend (Component Layer)

#### API Utilities (`src/lib/api-utils.ts`)

Helper functions for making timezone-aware API calls:

- `getUserTimezone()` - Gets user's timezone using `Intl.DateTimeFormat()`
- `createTimezoneHeaders()` - Creates headers with timezone information
- `fetchWithTimezone()` - Makes API calls with timezone headers
- `formatDateInUserTimezone()` - Formats dates for display
- `formatTimeAgoInUserTimezone()` - Formats relative time
- `analyticsApi.*` - Convenience methods for all analytics endpoints

#### Updated Components

Key components updated to use timezone-aware APIs:

1. **TimeBasedAnalytics** - Shows hourly/daily charts in user timezone
2. **GeographicAnalytics** - Geographic data with proper date filtering
3. **QRCodeDetails** - Creation and last scan times in user timezone
4. **ActivityFeed** - Activity timestamps in user timezone
5. **DeviceAnalytics** - Device data with timezone filtering

### 3. Database Schema

The database continues to store all timestamps in UTC:

```sql
-- All datetime fields store UTC timestamps
CREATE TABLE qr_code_scan_analytics (
  id TEXT PRIMARY KEY,
  qr_code_id TEXT NOT NULL,
  scan_time DATETIME DEFAULT CURRENT_TIMESTAMP, -- UTC
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- UTC
  -- ... other fields
);
```

### 4. How It Works

#### Data Flow Example

1. **User in New York (EST, UTC-5) views analytics for "last 7 days"**

2. **Frontend sends request:**
   ```javascript
   fetch('/api/analytics/time-based?range=7d', {
     headers: {
       'X-User-Timezone': 'America/New_York'
     }
   })
   ```

3. **Backend processes:**
   - Detects timezone: `America/New_York`
   - Calculates 7 days ago in user timezone: `2024-01-15 00:00:00 EST`
   - Converts to UTC for database query: `2024-01-15 05:00:00 UTC`
   - Executes SQL: `WHERE scan_time >= '2024-01-15 05:00:00'`

4. **Backend returns:**
   - Raw data converted to user timezone
   - Hourly data adjusted for EST
   - Daily data grouped by EST dates

5. **Frontend displays:**
   - Charts showing data in user's local time
   - Dates formatted in user's locale

#### SQL Query Examples

**Before (UTC-only):**
```sql
SELECT DATE(scan_time) as date, COUNT(*) as scans
FROM qr_code_scan_analytics
WHERE scan_time >= datetime('now', '-7 days')
GROUP BY DATE(scan_time)
```

**After (Timezone-aware):**
```sql
SELECT DATE(scan_time, '+5 hours') as date, COUNT(*) as scans
FROM qr_code_scan_analytics  
WHERE scan_time >= '2024-01-15 05:00:00'
GROUP BY DATE(scan_time, '+5 hours')
```

### 5. Usage Examples

#### Making Timezone-Aware API Calls

```javascript
import { analyticsApi } from '../lib/api-utils';

// Automatically includes user timezone
const data = await analyticsApi.fetchTimeBased('7d', qrCodeId);
```

#### Formatting Dates for Display

```javascript
import { formatDateInUserTimezone } from '../lib/api-utils';

const displayDate = formatDateInUserTimezone(utcDateString, {
  year: 'numeric',
  month: 'short',
  day: 'numeric',
  hour: '2-digit',
  minute: '2-digit'
});
```

### 6. Benefits

1. **Accurate Analytics**: Users see data based on their local time
2. **Consistent Storage**: All data stored in UTC for consistency
3. **Global Support**: Works for users in any timezone
4. **Automatic Detection**: No manual timezone selection required
5. **Fallback Handling**: Graceful degradation to UTC if timezone detection fails

### 7. Testing

To test timezone functionality:

1. **Change browser timezone:**
   - Chrome DevTools → Settings → Advanced → Timezone
   - Set to different timezone (e.g., "Asia/Tokyo")

2. **Verify API calls:**
   - Check Network tab for `X-User-Timezone` header
   - Verify `timezone` parameter in URLs

3. **Check data display:**
   - Hourly charts should show local hours
   - Daily data should group by local dates
   - Timestamps should display in local time

### 8. Future Enhancements

Potential improvements:

1. **User Preference Override**: Allow users to manually select timezone
2. **Timezone Display**: Show current timezone in UI
3. **Historical Timezone**: Handle timezone changes for historical data
4. **DST Handling**: Enhanced daylight saving time support
5. **Performance**: Cache timezone calculations for better performance

## Troubleshooting

### Common Issues

1. **Timezone not detected**: Check browser support for `Intl.DateTimeFormat()`
2. **Wrong time display**: Verify `X-User-Timezone` header is being sent
3. **Data inconsistency**: Ensure all APIs use timezone-aware queries
4. **Performance issues**: Consider caching timezone calculations

### Debug Steps

1. Check browser console for timezone logs
2. Verify API response includes `timezone` field
3. Test with different browser timezone settings
4. Compare UTC vs local time calculations
