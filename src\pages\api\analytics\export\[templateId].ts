import type { APIRoute } from 'astro';
import {
  getTimezoneFromRequest,
  getTimezoneAwareDateFilter,
  convertDataToUserTimezone,
  isValidTimezone
} from '../../../../lib/timezone-utils';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals, params }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore  
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ error: "D1 database not configured." }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get template ID from params
    const templateId = params.templateId;
    
    // Get URL parameters
    const url = new URL(request.url);
    const format = url.searchParams.get('format') || 'csv';
    const dateRange = url.searchParams.get('range') || '7d';
    const qrCodeId = url.searchParams.get('qr_code_id');

    // Get user timezone
    const userTimezone = getTimezoneFromRequest(request);
    console.log('Debug: User timezone for export:', userTimezone);

    // Validate timezone
    if (!isValidTimezone(userTimezone)) {
      console.warn('Invalid timezone provided:', userTimezone, 'falling back to UTC');
    }

    // Calculate timezone-aware date filter
    const dateFilter = getTimezoneAwareDateFilter(dateRange, userTimezone, 'scan.scan_time');

    // Add QR code filter if specified
    const qrCodeFilter = qrCodeId ? "AND scan.qr_code_id = ?" : "";

    let data: any[] = [];
    let headers: string[] = [];
    let filename = `qr-analytics-${templateId}-${dateRange}`;

    // Generate data based on template type
    switch (templateId) {
      case 'overview':
        // Get comprehensive overview data
        const overviewQuery = `
          SELECT
            scan.id,
            scan.qr_code_id,
            qr.name as qr_name,
            qr.content_type,
            scan.scan_time,
            scan.ip,
            scan.country,
            scan.city,
            scan.device,
            scan.os,
            scan.browser,
            scan.user_agent,
            scan.referrer
          FROM qr_code_scan_analytics scan
          LEFT JOIN qr_codes qr ON scan.qr_code_id = qr.id
          WHERE 1=1 ${dateFilter} ${qrCodeFilter}
          ORDER BY scan.scan_time DESC
        `;
        
        const overviewResult = qrCodeId ?
          await db.prepare(overviewQuery).bind(qrCodeId).all() :
          await db.prepare(overviewQuery).all();
        
        data = overviewResult.results || [];
        headers = [
          'ID', 'QR Code ID', 'QR Code Name', 'Content Type', 'Scan Time',
          'IP Address', 'Country', 'City', 'Device', 'OS', 'Browser', 'User Agent', 'Referrer'
        ];
        break;

      case 'geographic':
        // Get geographic data
        const geoQuery = `
          SELECT
            scan.country,
            scan.city,
            COUNT(*) as scans,
            COUNT(DISTINCT scan.ip) as unique_users,
            scan.qr_code_id,
            qr.name as qr_name
          FROM qr_code_scan_analytics scan
          LEFT JOIN qr_codes qr ON scan.qr_code_id = qr.id
          WHERE scan.country IS NOT NULL ${dateFilter} ${qrCodeFilter}
          GROUP BY scan.country, scan.city, scan.qr_code_id, qr.name
          ORDER BY scans DESC
        `;
        
        const geoResult = qrCodeId ?
          await db.prepare(geoQuery).bind(qrCodeId).all() :
          await db.prepare(geoQuery).all();
        
        data = geoResult.results || [];
        headers = ['Country', 'City', 'Total Scans', 'Unique Users', 'QR Code ID', 'QR Code Name'];
        break;

      case 'device':
        // Get device analytics data
        const deviceQuery = `
          SELECT
            scan.device,
            scan.os,
            scan.browser,
            COUNT(*) as scans,
            COUNT(DISTINCT scan.ip) as unique_users,
            scan.qr_code_id,
            qr.name as qr_name
          FROM qr_code_scan_analytics scan
          LEFT JOIN qr_codes qr ON scan.qr_code_id = qr.id
          WHERE scan.device IS NOT NULL ${dateFilter} ${qrCodeFilter}
          GROUP BY scan.device, scan.os, scan.browser, scan.qr_code_id, qr.name
          ORDER BY scans DESC
        `;
        
        const deviceResult = qrCodeId ?
          await db.prepare(deviceQuery).bind(qrCodeId).all() :
          await db.prepare(deviceQuery).all();
        
        data = deviceResult.results || [];
        headers = ['Device', 'Operating System', 'Browser', 'Total Scans', 'Unique Users', 'QR Code ID', 'QR Code Name'];
        break;

      case 'time-series':
        // Get time series data
        const timeQuery = `
          SELECT
            DATE(scan.scan_time) as date,
            strftime('%H', scan.scan_time) as hour,
            COUNT(*) as scans,
            COUNT(DISTINCT scan.ip) as unique_users,
            scan.qr_code_id,
            qr.name as qr_name
          FROM qr_code_scan_analytics scan
          LEFT JOIN qr_codes qr ON scan.qr_code_id = qr.id
          WHERE 1=1 ${dateFilter} ${qrCodeFilter}
          GROUP BY DATE(scan.scan_time), strftime('%H', scan.scan_time), scan.qr_code_id, qr.name
          ORDER BY date DESC, hour DESC
        `;
        
        const timeResult = qrCodeId ?
          await db.prepare(timeQuery).bind(qrCodeId).all() :
          await db.prepare(timeQuery).all();
        
        data = timeResult.results || [];
        headers = ['Date', 'Hour', 'Total Scans', 'Unique Users', 'QR Code ID', 'QR Code Name'];
        break;

      default:
        return new Response(JSON.stringify({ error: 'Invalid template ID' }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
    }

    // Convert timestamps to user timezone for display
    const convertedData = convertDataToUserTimezone(data, userTimezone, ['scan_time']);

    if (format === 'csv') {
      // Generate CSV content
      const csvRows = [
        headers.join(','),
        ...convertedData.map((row: any) => {
          return headers.map(header => {
            const key = header.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '');
            let value = '';
            
            // Map headers to data fields
            switch (key) {
              case 'id': value = row.id || ''; break;
              case 'qr_code_id': value = row.qr_code_id || ''; break;
              case 'qr_code_name': value = row.qr_name || ''; break;
              case 'content_type': value = row.content_type || ''; break;
              case 'scan_time': value = row.scan_time || ''; break;
              case 'ip_address': value = row.ip || ''; break;
              case 'country': value = row.country || ''; break;
              case 'city': value = row.city || ''; break;
              case 'device': value = row.device || ''; break;
              case 'operating_system': case 'os': value = row.os || ''; break;
              case 'browser': value = row.browser || ''; break;
              case 'user_agent': value = row.user_agent || ''; break;
              case 'referrer': value = row.referrer || ''; break;
              case 'total_scans': case 'scans': value = row.scans || ''; break;
              case 'unique_users': value = row.unique_users || ''; break;
              case 'date': value = row.date || ''; break;
              case 'hour': value = row.hour || ''; break;
              default: value = row[key] || '';
            }
            
            // Escape CSV values that contain commas or quotes
            if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
              value = `"${value.replace(/"/g, '""')}"`;
            }
            
            return value;
          }).join(',');
        })
      ];

      const csvContent = csvRows.join('\n');

      return new Response(csvContent, {
        status: 200,
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="${filename}.csv"`
        }
      });

    } else if (format === 'excel') {
      // Generate Excel content (simplified - using CSV format with Excel MIME type)
      const csvRows = [
        headers.join('\t'),
        ...convertedData.map((row: any) => {
          return headers.map(header => {
            const key = header.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '');
            let value = '';
            
            // Map headers to data fields (same logic as CSV)
            switch (key) {
              case 'id': value = row.id || ''; break;
              case 'qr_code_id': value = row.qr_code_id || ''; break;
              case 'qr_code_name': value = row.qr_name || ''; break;
              case 'content_type': value = row.content_type || ''; break;
              case 'scan_time': value = row.scan_time || ''; break;
              case 'ip_address': value = row.ip || ''; break;
              case 'country': value = row.country || ''; break;
              case 'city': value = row.city || ''; break;
              case 'device': value = row.device || ''; break;
              case 'operating_system': case 'os': value = row.os || ''; break;
              case 'browser': value = row.browser || ''; break;
              case 'user_agent': value = row.user_agent || ''; break;
              case 'referrer': value = row.referrer || ''; break;
              case 'total_scans': case 'scans': value = row.scans || ''; break;
              case 'unique_users': value = row.unique_users || ''; break;
              case 'date': value = row.date || ''; break;
              case 'hour': value = row.hour || ''; break;
              default: value = row[key] || '';
            }
            
            return value;
          }).join('\t');
        })
      ];

      const excelContent = csvRows.join('\n');

      return new Response(excelContent, {
        status: 200,
        headers: {
          'Content-Type': 'application/vnd.ms-excel',
          'Content-Disposition': `attachment; filename="${filename}.xls"`
        }
      });

    } else {
      return new Response(JSON.stringify({ error: 'Unsupported format' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

  } catch (error) {
    console.error('Export template API error:', error);
    
    return new Response(JSON.stringify({ 
      error: error instanceof Error ? error.message : 'Failed to export data' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
