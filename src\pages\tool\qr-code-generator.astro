---
import QRGenerator from "../../components/QRGenerator";
import Layout from "../../layouts/Layout.astro";

const title =
  "Stop Wasting Money on Blind QR Campaigns - Get Analytics That Actually Work | QR Analytica";
const description =
  "Your QR codes are failing you. Every scan without analytics is money down the drain. Get professional QR analytics that reveal exactly what's working and what's not.";
const canonicalURL = "https://qranalytica.com/tools/qr-code-generator";

// Structured Data for SEO
const structuredData = {
  "@context": "https://schema.org",
  "@type": "WebApplication",
  name: "QR Code Generator",
  applicationCategory: "UtilitiesApplication",
  operatingSystem: "All",
  url: canonicalURL.toString(),
  description: description,
  offers: [
    {
      "@type": "Offer",
      name: "Basic QR Codes",
      price: "0",
      priceCurrency: "USD",
      description: "Static QR codes with basic features",
    },
    {
      "@type": "Offer",
      name: "Dynamic QR Codes with Ads",
      price: "0",
      priceCurrency: "USD",
      description: "Dynamic QR codes with analytics, includes ads",
    },
    {
      "@type": "Offer",
      name: "Dynamic QR Codes Pro",
      price: "99",
      priceCurrency: "USD",
      description: "Dynamic QR codes with analytics, ad-free experience",
    },
  ],
  featureList: [
    "Custom QR Code Design",
    "Logo Embedding",
    "Analytics & Tracking",
    "High Resolution Download",
    "Custom Domains",
    "WhatsApp QR Codes",
    "URL QR Codes",
  ],
  applicationSubCategory: "QR Code Generator",
  softwareVersion: "2.0",
  releaseNotes: "Professional QR code generator with advanced analytics",
  publisher: {
    "@type": "Organization",
    name: "QR Analytica",
  },
};

const breadcrumbSchema = {
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  itemListElement: [
    {
      "@type": "ListItem",
      position: 1,
      name: "Home",
      item: Astro.site,
    },
    {
      "@type": "ListItem",
      position: 2,
      name: "Qr Analytica",
      item: "https://qranalytica.com",
    },
    {
      "@type": "ListItem",
      position: 3,
      name: "QR Code Generator",
      item: canonicalURL,
    },
  ],
};

const faqSchema = {
  "@context": "https://schema.org",
  "@type": "FAQPage",
  mainEntity: [
    {
      "@type": "Question",
      name: "How do I create a QR code?",
      acceptedAnswer: {
        "@type": "Answer",
        text: "Creating a QR code is simple: 1) Enter your URL or WhatsApp number, 2) Customize the design and colors, 3) Add your logo (optional), 4) Enable analytics for tracking, and 5) Download your high-resolution QR code.",
      },
    },
    {
      "@type": "Question",
      name: "Can I track QR code scans?",
      acceptedAnswer: {
        "@type": "Answer",
        text: "Yes! Our QR codes come with advanced analytics that track total scans, unique users, locations, devices, time patterns, and more. You can view detailed reports in your dashboard.",
      },
    },
    {
      "@type": "Question",
      name: "Can I add my logo to QR codes?",
      acceptedAnswer: {
        "@type": "Answer",
        text: "Absolutely! You can upload your company logo or brand image to be embedded in the center of your QR code. We support various image formats and automatically optimize them for best scanning performance.",
      },
    },
    {
      "@type": "Question",
      name: "What formats can I download QR codes in?",
      acceptedAnswer: {
        "@type": "Answer",
        text: "Our QR codes are generated in high-resolution PNG format (2400x2400px) which is perfect for both digital use and high-quality printing on business cards, flyers, and other marketing materials.",
      },
    },
    {
      "@type": "Question",
      name: "What are the pricing options?",
      acceptedAnswer: {
        "@type": "Answer",
        text: "We offer three options: 1) Static QR codes are completely free forever, 2) Dynamic QR codes with analytics are free but include ads, 3) Ad-free dynamic QR codes with premium analytics for a one-time fee of $99.",
      },
    },
    {
      "@type": "Question",
      name: "Are there any recurring fees?",
      acceptedAnswer: {
        "@type": "Answer",
        text: "No! We don't believe in subscription fees. Static QR codes are free forever. Dynamic QR codes are either free with ads or available for a single one-time payment of $99 for ad-free experience.",
      },
    },
  ],
};

// NEW: Organization structured data for enhanced SEO
const organizationSchema = {
  "@context": "https://schema.org",
  "@type": "Organization",
  name: "QR Analytica",
  url: "https://qranalytica.com",
  logo: "https://qranalytica.com/favicon.svg",
  sameAs: [
    "https://twitter.com/qranalytica",
    "https://www.linkedin.com/company/qr-analytica"
  ]
};
---

<Layout title={title}>
  <Fragment slot="head">
    <!-- Primary Meta Tags -->
    <meta name="title" content={title} />
    <meta name="description" content={description} />
    <meta
      name="keywords"
      content="QR code generator, free QR code, custom QR code, QR code analytics, QR code tracking, logo QR code, WhatsApp QR code, business QR code"
    />
    <link rel="canonical" href={canonicalURL} />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content={canonicalURL} />
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />
    <meta
      property="og:image"
      content={"https://qranalytica.com/qr-code-generator-og.png"}
    />
    <meta property="og:site_name" content="QR Analytica" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content={canonicalURL} />
    <meta property="twitter:title" content={title} />
    <meta property="twitter:description" content={description} />
    <meta
      property="twitter:image"
      content={"https://qranalytica.com/qr-code-generator-og.png"}
    />

    <!-- Additional Meta Tags -->
    <meta name="robots" content="index, follow" />
    <meta name="author" content="QR Analytica" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#2563eb" />

    <!-- NEW: Extended Meta Tags for SEO & Social -->
    <meta http-equiv="x-ua-compatible" content="IE=edge" />
    <meta name="HandheldFriendly" content="True" />
    <meta name="MobileOptimized" content="320" />
    <meta name="language" content="EN" />
    <meta property="og:locale" content="en_US" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta name="twitter:image:alt" content="QRAnalytica QR Code Generator" />
    <link rel="alternate" hreflang="en" href={canonicalURL} />
    <link rel="dns-prefetch" href="https://fonts.gstatic.com" />
    <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin />
    <link rel="manifest" href="/site.webmanifest" />

    <!-- Structured Data -->
    <script
      type="application/ld+json"
      set:html={JSON.stringify(structuredData)}
    />
    <script
      type="application/ld+json"
      set:html={JSON.stringify(breadcrumbSchema)}
    />
    <script type="application/ld+json" set:html={JSON.stringify(faqSchema)} />
    <script type="application/ld+json" set:html={JSON.stringify(organizationSchema)} />

    <!-- Preload Critical Resources -->
    <link
      rel="preload"
      href="/fonts/inter-var.woff2"
      as="font"
      type="font/woff2"
      crossorigin
    />
  </Fragment>

  <!-- QR Generator Component -->
  <section class="py-12 bg-white">
    <div class="max-w-7xl mx-auto   ">
      <div class="text-center mb-8">
        <h2 class="text-3xl font-bold text-slate-900 mb-4">
          Create Your First Analytics-Powered QR Code
        </h2>
        <p class="text-lg text-slate-600">
          See the difference professional QR analytics makes. Try it now - it's
          free to start.
        </p>
      </div>
      <QRGenerator client:load />
    </div>
  </section>
</Layout>
