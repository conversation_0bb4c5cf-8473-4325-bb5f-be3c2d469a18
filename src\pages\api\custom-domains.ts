import type { APIRoute } from 'astro';
import { v4 as uuidv4 } from 'uuid';

export const prerender = false;

interface CustomDomain {
  id: string;
  user_id: string;
  domain: string;
  status: 'pending' | 'active' | 'failed' | 'expired';
  ssl_status: 'pending' | 'active' | 'failed';
  verification_method: 'dns' | 'http';
  verification_token?: string;
  dns_records?: string; // JSON
  cloudflare_zone_id?: string;
  cloudflare_custom_hostname_id?: string;
  pages_project_name?: string;
  verified: number;
  last_verified_at?: string;
  expires_at?: string;
  error_message?: string;
  created_at: string;
  updated_at: string;
}

// GET - List all custom domains for the user
export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore  
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ error: "D1 database not configured." }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get user ID from request (you might want to implement proper auth)
    const url = new URL(request.url);
    const userId = url.searchParams.get('user_id') || 'default-user';

    // Fetch custom domains for the user
    const domains = await db.prepare(`
      SELECT 
        id, user_id, domain, status, ssl_status, verification_method,
        verification_token, dns_records, cloudflare_zone_id, 
        cloudflare_custom_hostname_id, pages_project_name, verified,
        last_verified_at, expires_at, error_message, created_at, updated_at
      FROM custom_domains 
      WHERE user_id = ?
      ORDER BY created_at DESC
    `).bind(userId).all();

    const formattedDomains = domains.results.map((domain: any) => ({
      ...domain,
      dns_records: domain.dns_records ? JSON.parse(domain.dns_records) : null,
      verified: Boolean(domain.verified)
    }));

    return new Response(JSON.stringify({
      success: true,
      domains: formattedDomains
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error fetching custom domains:', error);
    return new Response(JSON.stringify({ 
      success: false,
      error: 'Failed to fetch custom domains' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

// POST - Add a new custom domain
export const POST: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore  
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ error: "D1 database not configured." }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();
    const { domain, pages_project_name, user_id = 'default-user' } = body;

    if (!domain) {
      return new Response(JSON.stringify({ 
        success: false,
        error: 'Domain is required' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Validate domain format
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/;
    if (!domainRegex.test(domain)) {
      return new Response(JSON.stringify({ 
        success: false,
        error: 'Invalid domain format' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if domain already exists
    const existingDomain = await db.prepare(
      'SELECT id FROM custom_domains WHERE domain = ?'
    ).bind(domain).first();

    if (existingDomain) {
      return new Response(JSON.stringify({ 
        success: false,
        error: 'Domain already exists' 
      }), {
        status: 409,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const domainId = uuidv4();
    const verificationToken = uuidv4();

    // Generate DNS records for verification
    const dnsRecords = {
      cname: {
        name: domain,
        value: `${pages_project_name || 'your-project'}.pages.dev`,
        type: 'CNAME'
      },
      txt: {
        name: `_cf-custom-hostname.${domain}`,
        value: verificationToken,
        type: 'TXT'
      }
    };

    // Insert new domain
    await db.prepare(`
      INSERT INTO custom_domains (
        id, user_id, domain, status, ssl_status, verification_method,
        verification_token, dns_records, pages_project_name, verified
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      domainId,
      user_id,
      domain,
      'pending',
      'pending',
      'dns',
      verificationToken,
      JSON.stringify(dnsRecords),
      pages_project_name || null,
      0
    ).run();

    return new Response(JSON.stringify({
      success: true,
      domain: {
        id: domainId,
        domain,
        status: 'pending',
        ssl_status: 'pending',
        verification_method: 'dns',
        verification_token: verificationToken,
        dns_records: dnsRecords,
        pages_project_name,
        verified: false
      }
    }), {
      status: 201,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error adding custom domain:', error);
    return new Response(JSON.stringify({ 
      success: false,
      error: 'Failed to add custom domain' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

// DELETE - Remove a custom domain
export const DELETE: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore  
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ error: "D1 database not configured." }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const url = new URL(request.url);
    const domainId = url.searchParams.get('id');
    const userId = url.searchParams.get('user_id') || 'default-user';

    if (!domainId) {
      return new Response(JSON.stringify({ 
        success: false,
        error: 'Domain ID is required' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Verify domain belongs to user
    const domain = await db.prepare(
      'SELECT id FROM custom_domains WHERE id = ? AND user_id = ?'
    ).bind(domainId, userId).first();

    if (!domain) {
      return new Response(JSON.stringify({ 
        success: false,
        error: 'Domain not found or access denied' 
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Delete the domain
    await db.prepare(
      'DELETE FROM custom_domains WHERE id = ? AND user_id = ?'
    ).bind(domainId, userId).run();

    return new Response(JSON.stringify({
      success: true,
      message: 'Domain deleted successfully'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error deleting custom domain:', error);
    return new Response(JSON.stringify({ 
      success: false,
      error: 'Failed to delete custom domain' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
