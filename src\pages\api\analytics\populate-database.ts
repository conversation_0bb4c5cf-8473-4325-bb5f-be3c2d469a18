import type { APIRoute } from 'astro';
import { v4 as uuidv4 } from 'uuid';

export const prerender = false;

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore  
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ error: "D1 database not configured." }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Clear existing test data
    await db.prepare("DELETE FROM qr_code_scan_analytics WHERE qr_code_id IN (SELECT id FROM qr_codes WHERE name LIKE '%Test%' OR name LIKE '%Sample%' OR name LIKE '%Company%' OR name LIKE '%Contact%' OR name LIKE '%WiFi%')").run();
    await db.prepare("DELETE FROM qr_codes WHERE name LIKE '%Test%' OR name LIKE '%Sample%' OR name LIKE '%Company%' OR name LIKE '%Contact%' OR name LIKE '%WiFi%'").run();

    // Create comprehensive sample QR codes
    const sampleQRCodes = [
      {
        id: uuidv4(),
        name: 'Company Website',
        data: 'https://company.example.com',
        content_type: 'url',
        original_url: 'https://company.example.com',
        custom_slug: 'company-site',
        dynamic: 1
      },
      {
        id: uuidv4(),
        name: 'Product Catalog',
        data: 'https://catalog.example.com',
        content_type: 'url',
        original_url: 'https://catalog.example.com',
        custom_slug: 'product-catalog',
        dynamic: 1
      },
      {
        id: uuidv4(),
        name: 'Contact Information',
        data: 'mailto:<EMAIL>',
        content_type: 'email',
        email_address: '<EMAIL>',
        custom_slug: 'contact-email',
        dynamic: 1
      },
      {
        id: uuidv4(),
        name: 'Guest WiFi Access',
        data: 'WIFI:T:WPA;S:GuestNetwork;P:password123;;',
        content_type: 'wifi',
        wifi_ssid: 'GuestNetwork',
        custom_slug: 'guest-wifi',
        dynamic: 1
      },
      {
        id: uuidv4(),
        name: 'Restaurant Menu',
        data: 'https://menu.restaurant.com',
        content_type: 'url',
        original_url: 'https://menu.restaurant.com',
        custom_slug: 'restaurant-menu',
        dynamic: 1
      },
      {
        id: uuidv4(),
        name: 'Event Registration',
        data: 'https://events.example.com/register',
        content_type: 'url',
        original_url: 'https://events.example.com/register',
        custom_slug: 'event-register',
        dynamic: 1
      }
    ];

    // Insert QR codes with varied creation dates
    for (let i = 0; i < sampleQRCodes.length; i++) {
      const qr = sampleQRCodes[i];
      const daysAgo = Math.floor(Math.random() * 60) + 1; // Created 1-60 days ago
      
      await db.prepare(`
        INSERT INTO qr_codes (id, name, data, content_type, original_url, email_address, wifi_ssid, custom_slug, dynamic, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now', '-' || ? || ' days'))
      `).bind(
        qr.id,
        qr.name,
        qr.data,
        qr.content_type,
        qr.original_url || null,
        qr.email_address || null,
        qr.wifi_ssid || null,
        qr.custom_slug,
        qr.dynamic,
        daysAgo
      ).run();
    }

    // Enhanced geographic and device data
    const countries = ['United States', 'Canada', 'United Kingdom', 'Germany', 'France', 'Japan', 'Australia', 'Brazil', 'India', 'Spain'];
    const cities = {
      'United States': ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia', 'San Antonio', 'San Diego'],
      'Canada': ['Toronto', 'Vancouver', 'Montreal', 'Calgary', 'Ottawa', 'Edmonton', 'Mississauga', 'Winnipeg'],
      'United Kingdom': ['London', 'Manchester', 'Birmingham', 'Leeds', 'Glasgow', 'Sheffield', 'Bradford', 'Liverpool'],
      'Germany': ['Berlin', 'Munich', 'Hamburg', 'Cologne', 'Frankfurt', 'Stuttgart', 'Düsseldorf', 'Dortmund'],
      'France': ['Paris', 'Lyon', 'Marseille', 'Toulouse', 'Nice', 'Nantes', 'Strasbourg', 'Montpellier'],
      'Japan': ['Tokyo', 'Osaka', 'Kyoto', 'Yokohama', 'Nagoya', 'Sapporo', 'Fukuoka', 'Kobe'],
      'Australia': ['Sydney', 'Melbourne', 'Brisbane', 'Perth', 'Adelaide', 'Gold Coast', 'Newcastle', 'Canberra'],
      'Brazil': ['São Paulo', 'Rio de Janeiro', 'Brasília', 'Salvador', 'Fortaleza', 'Belo Horizonte', 'Manaus', 'Curitiba'],
      'India': ['Mumbai', 'Delhi', 'Bangalore', 'Hyderabad', 'Chennai', 'Kolkata', 'Pune', 'Ahmedabad'],
      'Spain': ['Madrid', 'Barcelona', 'Valencia', 'Seville', 'Zaragoza', 'Málaga', 'Murcia', 'Palma']
    };

    const devices = ['mobile', 'desktop', 'tablet'];
    const browsers = ['Chrome', 'Safari', 'Firefox', 'Edge', 'Opera', 'Samsung Internet'];
    const operatingSystems = ['Windows', 'macOS', 'iOS', 'Android', 'Linux', 'ChromeOS'];

    // Generate realistic scan patterns
    const totalScans = 2500;
    const uniqueIPs = [];
    
    // Generate pool of unique IPs for realistic user tracking
    for (let i = 0; i < 500; i++) {
      uniqueIPs.push(`${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`);
    }

    for (let i = 0; i < totalScans; i++) {
      const qrCode = sampleQRCodes[Math.floor(Math.random() * sampleQRCodes.length)];
      const country = countries[Math.floor(Math.random() * countries.length)];
      const city = cities[country][Math.floor(Math.random() * cities[country].length)];
      
      // Weighted device distribution (mobile-first)
      let device;
      const deviceRand = Math.random();
      if (deviceRand < 0.65) device = 'mobile';
      else if (deviceRand < 0.85) device = 'desktop';
      else device = 'tablet';
      
      const browser = browsers[Math.floor(Math.random() * browsers.length)];
      const os = operatingSystems[Math.floor(Math.random() * operatingSystems.length)];
      
      // Use existing IP for returning users (40% chance)
      const ip = Math.random() < 0.4 ? 
        uniqueIPs[Math.floor(Math.random() * uniqueIPs.length)] :
        `${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`;
      
      // Realistic time distribution
      let daysAgo, hoursAgo, minutesAgo;
      const timeRand = Math.random();
      
      if (timeRand < 0.3) {
        // 30% in last 24 hours
        daysAgo = 0;
        hoursAgo = Math.floor(Math.random() * 24);
        minutesAgo = Math.floor(Math.random() * 60);
      } else if (timeRand < 0.6) {
        // 30% in last 7 days
        daysAgo = Math.floor(Math.random() * 7);
        hoursAgo = Math.floor(Math.random() * 24);
        minutesAgo = Math.floor(Math.random() * 60);
      } else {
        // 40% in last 30 days
        daysAgo = Math.floor(Math.random() * 30);
        hoursAgo = Math.floor(Math.random() * 24);
        minutesAgo = Math.floor(Math.random() * 60);
      }

      // Generate realistic user agent
      const userAgents = {
        mobile: [
          'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
          'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
          'Mozilla/5.0 (Linux; Android 12; Pixel 6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Mobile Safari/537.36'
        ],
        desktop: [
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
          'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:95.0) Gecko/20100101 Firefox/95.0'
        ],
        tablet: [
          'Mozilla/5.0 (iPad; CPU OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
          'Mozilla/5.0 (Linux; Android 11; SM-T870) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36'
        ]
      };

      const userAgent = userAgents[device][Math.floor(Math.random() * userAgents[device].length)];
      const referrers = ['https://google.com', 'https://facebook.com', 'https://twitter.com', 'direct', 'https://linkedin.com'];
      const referrer = referrers[Math.floor(Math.random() * referrers.length)];

      await db.prepare(`
        INSERT INTO qr_code_scan_analytics (
          id, qr_code_id, scan_time, ip, user_agent, referrer, country, city, device, os, browser, created_at
        ) VALUES (?, ?, datetime('now', '-' || ? || ' days', '-' || ? || ' hours', '-' || ? || ' minutes'), ?, ?, ?, ?, ?, ?, ?, ?, datetime('now', '-' || ? || ' days', '-' || ? || ' hours', '-' || ? || ' minutes'))
      `).bind(
        uuidv4(),
        qrCode.id,
        daysAgo,
        hoursAgo,
        minutesAgo,
        ip,
        userAgent,
        referrer,
        country,
        city,
        device,
        os,
        browser,
        daysAgo,
        hoursAgo,
        minutesAgo
      ).run();
    }

    // Get final counts
    const qrCount = await db.prepare("SELECT COUNT(*) as count FROM qr_codes").first();
    const scanCount = await db.prepare("SELECT COUNT(*) as count FROM qr_code_scan_analytics").first();
    const uniqueUsers = await db.prepare("SELECT COUNT(DISTINCT ip) as count FROM qr_code_scan_analytics WHERE ip IS NOT NULL").first();

    return new Response(JSON.stringify({
      success: true,
      message: 'Database populated with comprehensive analytics data',
      data: {
        qrCodes: qrCount?.count || 0,
        totalScans: scanCount?.count || 0,
        uniqueUsers: uniqueUsers?.count || 0,
        sampleQRCodes: sampleQRCodes.length
      }
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Database population error:', error);
    
    return new Response(JSON.stringify({ 
      error: error instanceof Error ? error.message : 'Failed to populate database' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
