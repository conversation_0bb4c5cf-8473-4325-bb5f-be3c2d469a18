import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from '../../ui/card';
import { Button } from '../../ui/button';
import { Badge } from '../../ui/badge';
import { 
  Download, 
  Calendar, 
  FileText, 
  BarChart3,
  Filter,
  Clock,
  CheckCircle
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../ui/select';
import { analyticsApi } from '../../../lib/api-utils';

interface ReportingToolsProps {
  dateRange: string;
  onDateRangeChange: (range: string) => void;
  onExport: (format: 'csv' | 'excel') => void;
  qrCodeId?: string;
}

interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  formats: ('csv' | 'excel' | 'pdf')[];
}

const DATE_RANGES = [
  { value: '1d', label: 'Last 24 Hours' },
  { value: '7d', label: 'Last 7 Days' },
  { value: '30d', label: 'Last 30 Days' },
  { value: '90d', label: 'Last 3 Months' },
  { value: '1y', label: 'Last Year' },
  { value: 'all', label: 'All Time' }
];

const REPORT_TEMPLATES: ReportTemplate[] = [
  {
    id: 'overview',
    name: 'Analytics Overview',
    description: 'Complete analytics summary with all key metrics',
    icon: <BarChart3 className="h-5 w-5" />,
    formats: ['csv', 'excel', 'pdf']
  },
  {
    id: 'geographic',
    name: 'Geographic Report',
    description: 'Country and city-level scan distribution',
    icon: <Calendar className="h-5 w-5" />,
    formats: ['csv', 'excel']
  },
  {
    id: 'device',
    name: 'Device Analytics',
    description: 'Device types, OS, and browser breakdown',
    icon: <FileText className="h-5 w-5" />,
    formats: ['csv', 'excel']
  },
  {
    id: 'time-series',
    name: 'Time Series Data',
    description: 'Hourly, daily, and weekly scan patterns',
    icon: <Clock className="h-5 w-5" />,
    formats: ['csv', 'excel']
  }
];

export const ReportingTools: React.FC<ReportingToolsProps> = ({
  dateRange,
  onDateRangeChange,
  onExport,
  qrCodeId
}) => {
  const [exportingReports, setExportingReports] = useState<Set<string>>(new Set());
  const [recentExports, setRecentExports] = useState<Array<{
    id: string;
    name: string;
    format: string;
    timestamp: Date;
  }>>([]);

  const handleExportReport = async (templateId: string, format: 'csv' | 'excel' | 'pdf') => {
    setExportingReports(prev => new Set(prev).add(`${templateId}-${format}`));

    try {
      const qrCodeParam = qrCodeId ? `&qr_code_id=${qrCodeId}` : '';
      const response = await fetch(`/api/analytics/export/${templateId}?format=${format}&range=${dateRange}${qrCodeParam}`, {
        credentials: 'include'
      });
      
      if (!response.ok) {
        throw new Error('Export failed');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      
      const template = REPORT_TEMPLATES.find(t => t.id === templateId);
      const fileName = `${template?.name.replace(/\s+/g, '-').toLowerCase()}-${dateRange}.${format === 'excel' ? 'xlsx' : format}`;
      a.download = fileName;
      
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      // Add to recent exports
      setRecentExports(prev => [
        {
          id: `${templateId}-${format}-${Date.now()}`,
          name: template?.name || 'Report',
          format: format.toUpperCase(),
          timestamp: new Date()
        },
        ...prev.slice(0, 4) // Keep only last 5 exports
      ]);

    } catch (err) {
      console.error('Export error:', err);
    } finally {
      setExportingReports(prev => {
        const newSet = new Set(prev);
        newSet.delete(`${templateId}-${format}`);
        return newSet;
      });
    }
  };

  const formatTimeAgo = (timestamp: Date) => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - timestamp.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return 'Just now';
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}m ago`;
    } else if (diffInSeconds < 86400) {
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    } else {
      return `${Math.floor(diffInSeconds / 86400)}d ago`;
    }
  };

  return (
    <div className="space-y-6">
      {/* Date Range Selector */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>Report Configuration</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-700">Date Range:</span>
            </div>
            <Select value={dateRange} onValueChange={onDateRangeChange}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {DATE_RANGES.map((range) => (
                  <SelectItem key={range.value} value={range.value}>
                    {range.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Badge variant="outline" className="text-blue-700 border-blue-200">
              {DATE_RANGES.find(r => r.value === dateRange)?.label}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Report Templates */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Report Templates</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 grid-cols-1 lg:grid-cols-2">
            {REPORT_TEMPLATES.map((template) => (
              <div key={template.id} className="p-4 border rounded-lg hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg text-blue-600">
                      {template.icon}
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">{template.name}</h4>
                      <p className="text-sm text-gray-600">{template.description}</p>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  {template.formats.map((format) => {
                    const isExporting = exportingReports.has(`${template.id}-${format}`);
                    return (
                      <Button
                        key={format}
                        variant="outline"
                        size="sm"
                        onClick={() => handleExportReport(template.id, format as any)}
                        disabled={isExporting}
                        className="flex items-center space-x-1"
                      >
                        <Download className={`h-3 w-3 ${isExporting ? 'animate-spin' : ''}`} />
                        <span>{format.toUpperCase()}</span>
                      </Button>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Export Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Download className="h-5 w-5" />
            <span>Quick Export</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <Button
              onClick={() => onExport('csv')}
              className="bg-green-600 hover:bg-green-700 text-white flex items-center space-x-2"
            >
              <Download className="h-4 w-4" />
              <span>Export CSV</span>
            </Button>
            
            <Button
              onClick={() => onExport('excel')}
              className="bg-blue-600 hover:bg-blue-700 text-white flex items-center space-x-2"
            >
              <Download className="h-4 w-4" />
              <span>Export Excel</span>
            </Button>
            
            <div className="text-sm text-gray-600">
              Export all analytics data for the selected date range
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Exports */}
      {recentExports.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5" />
              <span>Recent Exports</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentExports.map((exportItem) => (
                <div key={exportItem.id} className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{exportItem.name}</p>
                      <p className="text-sm text-gray-600">
                        {exportItem.format} • {formatTimeAgo(exportItem.timestamp)}
                      </p>
                    </div>
                  </div>
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    Downloaded
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Export Guidelines */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Export Guidelines</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-start space-x-2">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
              <p><strong>CSV Format:</strong> Best for data analysis and importing into other tools</p>
            </div>
            <div className="flex items-start space-x-2">
              <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0" />
              <p><strong>Excel Format:</strong> Includes formatting and charts for presentation</p>
            </div>
            <div className="flex items-start space-x-2">
              <div className="w-1.5 h-1.5 bg-purple-500 rounded-full mt-2 flex-shrink-0" />
              <p><strong>PDF Format:</strong> Ready-to-share reports with visualizations</p>
            </div>
            <div className="flex items-start space-x-2">
              <div className="w-1.5 h-1.5 bg-orange-500 rounded-full mt-2 flex-shrink-0" />
              <p><strong>Large Datasets:</strong> Exports may take longer for extensive date ranges</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ReportingTools;
