import type { APIRoute } from 'astro';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore  
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ error: "D1 database not configured." }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get basic counts
    const qrCodesCount = await db.prepare("SELECT COUNT(*) as count FROM qr_codes").first();
    const scansCount = await db.prepare("SELECT COUNT(*) as count FROM qr_code_scan_analytics").first();
    const uniqueIPs = await db.prepare("SELECT COUNT(DISTINCT ip) as count FROM qr_code_scan_analytics WHERE ip IS NOT NULL").first();

    // Get sample QR codes with scan counts
    const sampleQRCodes = await db.prepare(`
      SELECT 
        qr.id,
        qr.name,
        qr.content_type,
        COUNT(scan.id) as scan_count
      FROM qr_codes qr
      LEFT JOIN qr_code_scan_analytics scan ON qr.id = scan.qr_code_id
      GROUP BY qr.id, qr.name, qr.content_type
      ORDER BY scan_count DESC
      LIMIT 5
    `).all();

    // Get sample scan data
    const sampleScans = await db.prepare(`
      SELECT 
        scan.id,
        scan.qr_code_id,
        qr.name as qr_name,
        scan.country,
        scan.city,
        scan.device,
        scan.os,
        scan.browser,
        scan.scan_time
      FROM qr_code_scan_analytics scan
      LEFT JOIN qr_codes qr ON scan.qr_code_id = qr.id
      ORDER BY scan.scan_time DESC
      LIMIT 10
    `).all();

    // Get geographic distribution
    const geographicData = await db.prepare(`
      SELECT 
        country,
        COUNT(*) as scans
      FROM qr_code_scan_analytics 
      WHERE country IS NOT NULL
      GROUP BY country
      ORDER BY scans DESC
      LIMIT 5
    `).all();

    // Get device breakdown
    const deviceData = await db.prepare(`
      SELECT 
        device,
        COUNT(*) as scans
      FROM qr_code_scan_analytics 
      WHERE device IS NOT NULL
      GROUP BY device
      ORDER BY scans DESC
    `).all();

    // Get recent activity (last 24 hours)
    const recentActivity = await db.prepare(`
      SELECT COUNT(*) as count
      FROM qr_code_scan_analytics 
      WHERE scan_time >= datetime('now', '-1 day')
    `).first();

    const verificationData = {
      database_status: "Connected and operational",
      data_summary: {
        total_qr_codes: qrCodesCount?.count || 0,
        total_scans: scansCount?.count || 0,
        unique_users: uniqueIPs?.count || 0,
        recent_activity_24h: recentActivity?.count || 0
      },
      sample_qr_codes: sampleQRCodes.results || [],
      sample_scans: sampleScans.results || [],
      geographic_distribution: geographicData.results || [],
      device_breakdown: deviceData.results || [],
      integration_status: {
        real_data: true,
        mock_data: false,
        api_endpoints_active: [
          "/api/analytics/overview",
          "/api/analytics/geographic", 
          "/api/analytics/time-based",
          "/api/analytics/devices",
          "/api/analytics/activity-feed",
          "/api/analytics/export"
        ]
      },
      timestamp: new Date().toISOString()
    };

    return new Response(JSON.stringify(verificationData, null, 2), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });

  } catch (error) {
    console.error('Integration verification error:', error);
    
    return new Response(JSON.stringify({ 
      error: error instanceof Error ? error.message : 'Failed to verify integration',
      integration_status: {
        real_data: false,
        mock_data: false,
        database_error: true
      }
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
