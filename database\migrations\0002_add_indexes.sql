-- Add indexes for better query performance
-- Migration: 0002_add_indexes.sql

-- Index for QR codes user_id and dynamic columns (used in dashboard metrics)
CREATE INDEX IF NOT EXISTS idx_qr_codes_user_dynamic ON qr_codes(user_id, dynamic);

-- Index for QR codes dynamic column (used for total active QR codes count)
CREATE INDEX IF NOT EXISTS idx_qr_codes_dynamic ON qr_codes(dynamic);

-- Index for scan analytics qr_code_id (used in joins)
CREATE INDEX IF NOT EXISTS idx_scan_analytics_qr_code_id ON qr_code_scan_analytics(qr_code_id);

-- Index for scan analytics scan_time (used for date-based queries)
CREATE INDEX IF NOT EXISTS idx_scan_analytics_scan_time ON qr_code_scan_analytics(scan_time);

-- Index for scan analytics created_at (used for date-based queries)
CREATE INDEX IF NOT EXISTS idx_scan_analytics_created_at ON qr_code_scan_analytics(created_at);

-- Composite index for scan analytics qr_code_id and scan_time (used in analytics queries)
CREATE INDEX IF NOT EXISTS idx_scan_analytics_qr_time ON qr_code_scan_analytics(qr_code_id, scan_time);

-- Index for scan analytics ip (used for unique user counts)
CREATE INDEX IF NOT EXISTS idx_scan_analytics_ip ON qr_code_scan_analytics(ip);

-- Index for scan analytics country and city (used for geographic analytics)
CREATE INDEX IF NOT EXISTS idx_scan_analytics_location ON qr_code_scan_analytics(country, city);

-- Index for scan analytics device (used for device analytics)
CREATE INDEX IF NOT EXISTS idx_scan_analytics_device ON qr_code_scan_analytics(device);

-- Index for QR codes created_at (used for ordering and date-based queries)
CREATE INDEX IF NOT EXISTS idx_qr_codes_created_at ON qr_codes(created_at);

-- Composite index for QR codes user_id and created_at (used for user-specific listings)
CREATE INDEX IF NOT EXISTS idx_qr_codes_user_created ON qr_codes(user_id, created_at);
