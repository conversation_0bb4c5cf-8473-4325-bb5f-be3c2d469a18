import type { APIRoute } from 'astro';
import { getDatabase } from '../../../lib/database';
import { getUserIdFromRequest, isAuthenticated } from '../../../lib/auth-utils';

export const prerender = false;

export const DELETE: APIRoute = async ({ params, request, locals }) => {
  try {
    // Get QR code ID from params
    const qrCodeId = params.id;
    
    if (!qrCodeId) {
      return new Response(JSON.stringify({
        success: false,
        error: 'QR code ID is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get database connection
    const db = getDatabase(locals.runtime.env);
    
    // Get user ID from session (if available)
    const userId = getUserIdFromRequest(request);
    console.log('Debug: Delete QR code - User ID:', userId, 'QR Code ID:', qrCodeId);

    // Check if QR code exists and belongs to user (if authenticated)
    const checkQuery = userId
      ? 'SELECT id, user_id FROM qr_codes WHERE id = ? AND user_id = ?'
      : 'SELECT id FROM qr_codes WHERE id = ?';
    
    const checkParams = userId ? [qrCodeId, userId] : [qrCodeId];
    const existingQR = await db.prepare(checkQuery).bind(...checkParams).first();

    if (!existingQR) {
      return new Response(JSON.stringify({
        success: false,
        error: 'QR code not found or access denied'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Delete related scan analytics first
    await db.prepare('DELETE FROM qr_code_scan_analytics WHERE qr_code_id = ?')
      .bind(qrCodeId)
      .run();

    // Delete the QR code
    const deleteResult = await db.prepare('DELETE FROM qr_codes WHERE id = ?')
      .bind(qrCodeId)
      .run();

    if (deleteResult.success) {
      return new Response(JSON.stringify({
        success: true,
        message: 'QR code deleted successfully'
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    } else {
      throw new Error('Failed to delete QR code');
    }

  } catch (error) {
    console.error('Delete QR code error:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete QR code'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

export const GET: APIRoute = async ({ params, request, locals }) => {
  try {
    // Get QR code ID from params
    const qrCodeId = params.id;
    
    if (!qrCodeId) {
      return new Response(JSON.stringify({
        success: false,
        error: 'QR code ID is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get database connection
    const db = getDatabase(locals.runtime.env);
    
    // Get user ID from session (if available)
    const userId = getUserIdFromRequest(request);

    // Get QR code details
    const qrCodeQuery = userId
      ? `SELECT qr.*, COUNT(sa.id) as scan_count, MAX(sa.scan_time) as last_scanned
         FROM qr_codes qr
         LEFT JOIN qr_code_scan_analytics sa ON qr.id = sa.qr_code_id
         WHERE qr.id = ? AND qr.user_id = ?
         GROUP BY qr.id`
      : `SELECT qr.*, COUNT(sa.id) as scan_count, MAX(sa.scan_time) as last_scanned
         FROM qr_codes qr
         LEFT JOIN qr_code_scan_analytics sa ON qr.id = sa.qr_code_id
         WHERE qr.id = ?
         GROUP BY qr.id`;
    
    const queryParams = userId ? [qrCodeId, userId] : [qrCodeId];
    const qrCodeResult = await db.prepare(qrCodeQuery).bind(...queryParams).first();

    if (!qrCodeResult) {
      return new Response(JSON.stringify({
        success: false,
        error: 'QR code not found'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      data: qrCodeResult
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Get QR code error:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch QR code'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

export const PUT: APIRoute = async ({ params, request, locals }) => {
  try {
    // Get QR code ID from params
    const qrCodeId = params.id;

    if (!qrCodeId) {
      return new Response(JSON.stringify({
        success: false,
        error: 'QR code ID is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Parse request body
    const body = await request.json() as { name?: string; original_url?: string };
    const { name, original_url } = body;

    // Validate input
    if (!name || !original_url) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Name and redirect URL are required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Basic URL validation
    try {
      new URL(original_url);
    } catch {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid URL format'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get database connection
    const db = getDatabase(locals.runtime.env);

    // Get user ID from session (if available)
    const userId = getUserIdFromRequest(request);
    console.log('Debug: Update QR code - User ID:', userId, 'QR Code ID:', qrCodeId);

    // Check if QR code exists (following the same pattern as details endpoint)
    const existingQR = await db.prepare('SELECT id, name, original_url FROM qr_codes WHERE id = ?').bind(qrCodeId).first();
    console.log('Existing QR found:', existingQR);

    if (!existingQR) {
      console.log('QR code not found');
      return new Response(JSON.stringify({
        success: false,
        error: 'QR code not found'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Update the QR code
    const updateResult = await db.prepare(`
      UPDATE qr_codes
      SET name = ?, original_url = ?, updated_at = datetime('now')
      WHERE id = ?
    `).bind(name, original_url, qrCodeId).run();

    if (!updateResult.success) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Failed to update QR code'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // // Get the updated QR code data
    // const updatedQR = await db.prepare(`
    //   SELECT
    //     id,
    //     name,
    //     content_type,
    //     data,
    //     original_url,
    //     email_address,
    //     wifi_ssid,
    //     phone_number,
    //     custom_slug,
    //     dynamic,
    //     created_at,
    //     updated_at
    //   FROM qr_codes
    //   WHERE id = ?
    // `).bind(qrCodeId).first();

    return new Response(JSON.stringify({
      success: true,
      data: {}
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error updating QR code:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
