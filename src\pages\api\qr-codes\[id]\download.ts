import type { APIRoute } from 'astro';
import { getDatabase } from '../../../../lib/database';

export const prerender = false;

export const GET: APIRoute = async ({ params, locals }) => {
  try {
    const qrCodeId = params.id;
    
    if (!qrCodeId) {
      return new Response(JSON.stringify({
        success: false,
        error: 'QR code ID is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get database connection
    const db = getDatabase(locals.runtime.env);

    // Get QR code with all necessary data for download
    const qrCodeQuery = `
      SELECT 
        id,
        name,
        data,
        content_type,
        original_url,
        custom_slug,
        tracking_domain,
        content_data,
        created_at
      FROM qr_codes 
      WHERE id = ?
    `;
    
    const qrCodeResult = await db.prepare(qrCodeQuery).bind(qrCodeId).first();

    if (!qrCodeResult) {
      return new Response(JSON.stringify({
        success: false,
        error: 'QR code not found'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Parse the stored QR options from the data field
    let qrOptions: any = {};
    try {
      if (qrCodeResult.data) {
        qrOptions = JSON.parse(qrCodeResult.data as string);
      }
    } catch (error) {
      console.error('Failed to parse QR options:', error);
      // Return basic fallback options if parsing fails
      qrOptions = {
        width: 300,
        height: 300,
        data: qrCodeResult.original_url || "",
        margin: 20,
        dotsOptions: { color: "#000000", type: "square" },
        backgroundOptions: { color: "#ffffff" },
        cornersSquareOptions: { type: "square", color: "#000000" },
        cornersDotOptions: { type: "dot", color: "#000000" },
        qrOptions: { errorCorrectionLevel: "H" }
      };
    }

    // Parse content data if available
    let contentData: any = {};
    try {
      if (qrCodeResult.content_data) {
        contentData = JSON.parse(qrCodeResult.content_data as string);
      }
    } catch (error) {
      console.error('Failed to parse content data:', error);
    }

    // Prepare response data
    const responseData = {
      id: qrCodeResult.id,
      name: qrCodeResult.name,
      data: qrCodeResult.data,
      content_type: qrCodeResult.content_type,
      original_url: qrCodeResult.original_url,
      custom_slug: qrCodeResult.custom_slug,
      tracking_domain: qrCodeResult.tracking_domain,
      qrOptions: qrOptions,
      contentData: contentData,
      created_at: qrCodeResult.created_at
    };

    return new Response(JSON.stringify({
      success: true,
      data: responseData
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Get QR code download data error:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch QR code data'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
